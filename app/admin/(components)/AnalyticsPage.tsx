"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";
import {
  AnalyticsData,
  fetchTimeSeriesData,
  fetchTopBrowsers,
  fetchTopPages,
  fetchWebsiteAnalytics,
} from "../actions";
import AnalyticsCard from "./AnalyticsCard";
import {
  BarChart2,
  Clock,
  Eye,
  MousePointerClick,
  TrendingUp,
  Zap,
} from "lucide-react";
import { TopPage, VisitorDetail } from "../queries";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const trafficData = [
  { date: "2025-06-21", visitors: 200, pageviews: 450 },
  { date: "2025-06-22", visitors: 300, pageviews: 520 },
  { date: "2025-06-23", visitors: 270, pageviews: 480 },
  { date: "2025-06-24", visitors: 320, pageviews: 580 },
  { date: "2025-06-25", visitors: 340, pageviews: 600 },
  { date: "2025-06-26", visitors: 290, pageviews: 540 },
  { date: "2025-06-27", visitors: 320, pageviews: 620 },
];

const websitePaths = [
  "/",
  "/terminal",
  "/endless",
  "/spike-vaults",
  "/boxoban",
  "/endless/play",
  "/boxoban/play",
  "/spike-vaults/play",
];
const timeRanges = [
  { label: "Today", value: "day" },
  { label: "Last 7 Days", value: "7d" },
  { label: "Last 30 Days", value: "30d" },
  { label: "All", value: "all" },
];

function useFetch<T>(fetchFn: () => Promise<T>, deps: any[] = []) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true);
        const result = await fetchFn();
        setData(result);
      } catch (err: any) {
        console.error(err);
        setError(err.message || "Error");
      } finally {
        setLoading(false);
      }
    };
    load();
  }, deps); // rerun on change

  return { data, loading, error };
}

export default function AnalyticsPage() {
  const [dateRange, setDateRange] = useState("all");
  const {
    data: analytics,
    loading: loadingAnalytics,
    error: errorAnalytics,
  } = useFetch<AnalyticsData>(
    () => fetchWebsiteAnalytics(dateRange),
    [dateRange]
  );

  const {
    data: topPages,
    loading: loadingPages,
    error: errorPages,
  } = useFetch<TopPage[]>(() => fetchTopPages(dateRange), [dateRange]);

  const {
    data: browsers,
    loading: loadingBrowsers,
    error: errorBrowsers,
  } = useFetch<VisitorDetail[]>(() => fetchTopBrowsers(dateRange), [dateRange]);

  useEffect(() => {
    const loadTimeSerierData = async () => {
      const websiteTimeSerisData = await fetchTimeSeriesData(dateRange);
      console.log(websiteTimeSerisData);
    };

    loadTimeSerierData();
  }, [dateRange]);

  const {
    data: timeSeriesData,
    loading: timeSeriesLoading,
    error: timeSeriesError,
  } = useFetch(() => fetchTimeSeriesData(dateRange), [dateRange]);

  return (
    <div className="p-6 bg-white min-h-screen text-gray-800">
      <div className="flex justify-between items-center mb-6 flex-wrap gap-4">
        <h2 className="text-2xl font-bold">Website Analytics Overview</h2>
        <Select
          disabled={loadingAnalytics || loadingBrowsers || loadingBrowsers}
          defaultValue={dateRange}
          onValueChange={setDateRange}
        >
          <SelectTrigger className="w-[180px] bg-white text-black">
            <SelectValue placeholder="Select Time Range   " />
          </SelectTrigger>
          <SelectContent className="bg-white text-black">
            {timeRanges.map((t) => (
              <SelectItem key={t.value} value={t.value}>
                {t.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <AnalyticsCard
          label="Unique Visitors"
          value={loadingAnalytics ? "loading" : analytics?.visitors ?? 0}
          icon={<BarChart2 />}
          color="blue"
        />

        <AnalyticsCard
          label="Pageviews"
          value={loadingAnalytics ? "loading" : analytics?.pageviews ?? 0}
          icon={<Eye />}
          color="green"
        />

        <AnalyticsCard
          label="Visits"
          value={loadingAnalytics ? "loading" : analytics?.visits ?? 0}
          icon={<MousePointerClick />}
          color="orange"
        />

        <AnalyticsCard
          label="Views per Visit"
          value={
            loadingAnalytics ? "loading" : `${analytics?.views_per_visit ?? 0}`
          }
          icon={<TrendingUp />}
          color="purple"
        />

        <AnalyticsCard
          label="Bounce Rate"
          value={
            loadingAnalytics ? "loading" : `${analytics?.bounce_rate ?? 0}%`
          }
          icon={<Zap />}
          color="gray"
        />

        <AnalyticsCard
          label="Visit Duration"
          value={
            loadingAnalytics
              ? "loading"
              : `${((analytics?.visit_duration ?? 0) / 60).toFixed(2)} min`
          }
          icon={<Clock className="w-5 h-5" />}
          color="yellow"
        />
      </div>

      {/* Line Chart */}
      <div className="bg-white border rounded-lg shadow p-4 mb-8">
        <h3 className="text-lg font-semibold text-black mb-2">
          Total Website Traffic
        </h3>

        {/* {timeSeriesLoading || !timeSeriesData ? (
          <p className="text-gray-500">Loading chart...</p>
        ) : (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="visitors"
                stroke="#60a5fa"
                name="Visitors"
              />
              <Line
                type="monotone"
                dataKey="events"
                stroke="#4ade80"
                name="Events"
              />
            </LineChart>
          </ResponsiveContainer>
        )} */}
      </div>

      {/* Top Pages + Visitor Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Top Pages */}
        <div className="bg-white border rounded-lg shadow p-4">
          <h4 className="text-lg font-semibold mb-3">Top Pages</h4>
          {loadingPages ? (
            <p>Loading pages...</p>
          ) : errorPages ? (
            <p className="text-red-500">{errorPages}</p>
          ) : (
            <ul className="space-y-2">
              {topPages
                ?.filter((page) => websitePaths.includes(page.page))
                .map((page) => (
                  <li
                    key={page.page}
                    className="flex justify-between text-sm text-gray-700"
                  >
                    <span>{page.page}</span>
                    <span className="font-semibold">{page.pageviews}</span>
                  </li>
                ))}
            </ul>
          )}
        </div>

        {/* Visitor Details */}
        <div className="bg-white border rounded-lg shadow p-4">
          <h4 className="text-lg font-semibold mb-3">Visitor Details</h4>
          {loadingBrowsers ? (
            <p>Loading visitor data...</p>
          ) : errorBrowsers ? (
            <p className="text-red-500">{errorBrowsers}</p>
          ) : (
            <ul className="space-y-2">
              {browsers?.map((visitor, index) => (
                <li
                  key={index}
                  className="flex flex-col text-sm text-gray-700 border-b pb-2"
                >
                  <div className="flex justify-between">
                    <span className="font-medium">Browser:</span>
                    <span>{visitor.browser}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Device:</span>
                    <span>{visitor.device}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">OS:</span>
                    <span>{visitor.os}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Referrer:</span>
                    <span>{visitor.referrer}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Pageviews:</span>
                    <span className="font-semibold">{visitor.pageviews}</span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}
