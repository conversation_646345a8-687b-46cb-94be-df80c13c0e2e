import { format } from "date-fns";

const today = new Date();
const yesterday = new Date(today);
yesterday.setDate(today.getDate() - 1);

const formatDate = (d: Date) => format(d, "yyyy-MM-dd");

export const timeRanges = [
  { label: "Today", value: [formatDate(today), formatDate(today)] },
  { label: "Yesterday", value: [formatDate(yesterday), formatDate(yesterday)] },
  { label: "Last 7 Days", value: "7d" },
  { label: "Last 30 Days", value: "30d" },
  { label: "All", value: "all" },
];
